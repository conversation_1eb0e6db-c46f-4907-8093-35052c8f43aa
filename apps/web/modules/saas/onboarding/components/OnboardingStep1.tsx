"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { generateOrganizationSlug } from "@saas/organizations/lib/api";
import { WebsiteService } from "@saas/websites/lib/website-service";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { ArrowRightIcon, MapPinIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
	type BusinessInfo,
	extractPlacesIdFromUrl,
	isGoogleMapsUrl,
} from "../utils/google-maps";
import { ExtendedBusinessPreview } from "./BusinessPreview";
import { ManualBusinessForm } from "./ManualBusinessForm";

const formSchema = z.object({
	googleMapsUrl: z
		.string()
		.min(1, "Google Maps business link is required")
		.url("Please enter a valid URL")
		.refine((url) => isGoogleMapsUrl(url), {
			message: "Please enter a valid Google Maps business link",
		})
		.refine(
			(url) => {
				// Check if we can extract a Places ID
				return extractPlacesIdFromUrl(url) !== null;
			},
			{
				message:
					"Unable to extract business information from this link. Please ensure it's a direct link to your business on Google Maps.",
			},
		),
});

type FormValues = z.infer<typeof formSchema>;

export function OnboardingStep1({
	onCompleted,
}: { onCompleted: (organizationSlug?: string) => void }) {
	const t = useTranslations();
	const [isExtracting, setIsExtracting] = useState(false);
	const [businessInfo, setBusinessInfo] = useState<BusinessInfo | null>(null);
	const [showPreview, setShowPreview] = useState(false);
	const [isCreatingOrganization, setIsCreatingOrganization] = useState(false);
	const [showManualForm, setShowManualForm] = useState(false);

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			googleMapsUrl: "",
		},
	});

	const demoUrls = [
		"https://maps.google.com/maps?cid=1234567890123456789", // Coffee Shop
		"https://www.google.com/maps/place/Restaurant/@40.7128,-74.0060,15z/data=!3m1!4b1!4m6!3m5!1s0x89c25a316e5b7c5d:0x9876543210fedcba", // Restaurant
		"https://maps.google.com/maps?cid=5555666677778888", // Tech Service
		"https://maps.google.com/maps?cid=7777888899990000", // Florist
		"https://www.google.com/maps/place/Fitness/@41.8781,-87.6298,15z/data=!3m1!4b1!4m6!3m5!1s0x880e2ca55810a493:0x1111222233334444", // Fitness Center
	];

	const tryDemo = () => {
		const randomUrl = demoUrls[Math.floor(Math.random() * demoUrls.length)];
		form.setValue("googleMapsUrl", randomUrl);
	};

	const handleConfirmBusiness = async () => {
		if (!businessInfo) return;

		setIsCreatingOrganization(true);

		try {
			// Create organization with the business name and information
			const organizationName =
				businessInfo.name || `Business (${businessInfo.placesId})`;

			const { error: orgError, data: newOrganization } =
				await authClient.organization.create({
					name: organizationName,
					slug: await generateOrganizationSlug(organizationName),
					metadata: {
						businessInfo,
						source: "google_maps",
						createdAt: new Date().toISOString(),
					},
				});

			if (orgError) {
				throw new Error(
					orgError.message || "Failed to create organization",
				);
			}

			// Set the new organization as active
			if (newOrganization) {
				await authClient.organization.setActive({
					organizationId: newOrganization.id,
				});

				// Generate website from business information
				try {
					await WebsiteService.generateWebsite(
						newOrganization.id,
						businessInfo,
					);
					console.log(
						"Website generation started for organization:",
						newOrganization.id,
					);
				} catch (websiteError) {
					console.error(
						"Failed to start website generation:",
						websiteError,
					);
					// Don't fail the onboarding if website generation fails
				}
			}

			// Complete onboarding - keep loading state until page transitions
			onCompleted(newOrganization?.slug);
		} catch (e) {
			form.setError("root", {
				type: "server",
				message: t("onboarding.notifications.accountSetupFailed"),
			});
			setIsCreatingOrganization(false);
		}
		// Note: Don't set setIsCreatingOrganization(false) on success
		// Keep the loading state until the page transitions
	};

	const handleManualBusinessSubmit = async (
		manualBusinessInfo: BusinessInfo,
	) => {
		setBusinessInfo(manualBusinessInfo);
		setShowPreview(true);
		setShowManualForm(false);
	};

	const handleBackToUrlEntry = () => {
		setShowManualForm(false);
		setShowPreview(false);
		setBusinessInfo(null);
		form.reset();
	};

	const onSubmit: SubmitHandler<FormValues> = async ({ googleMapsUrl }) => {
		form.clearErrors("root");
		setIsExtracting(true);

		try {
			// Use API route to extract business information with demo data
			const response = await fetch("/api/business/extract-from-maps", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ googleMapsUrl }),
			});

			if (!response.ok) {
				const errorData = await response.json();

				// Use improved error messages with suggestions
				const errorMessage = errorData.suggestion
					? `${errorData.error}. ${errorData.suggestion}`
					: errorData.error ||
						"Failed to extract business information";

				form.setError("googleMapsUrl", {
					type: "manual",
					message: errorMessage,
				});

				// For certain error types, we can suggest manual entry
				const canTryManualEntry = [
					"PLACES_ID_NOT_FOUND",
					"BUSINESS_INFO_EXTRACTION_FAILED",
					"UNSUPPORTED_URL_FORMAT",
				].includes(errorData.errorType);

				if (canTryManualEntry) {
					form.setError("root", {
						type: "manual",
						message: "manual_entry_available",
					});
				}

				return;
			}

			const { businessInfo: extractedBusinessInfo } =
				await response.json();

			// Store the business information and show preview
			setBusinessInfo(extractedBusinessInfo);
			setShowPreview(true);
		} catch (e) {
			form.setError("root", {
				type: "server",
				message: t("onboarding.notifications.accountSetupFailed"),
			});
		} finally {
			setIsExtracting(false);
		}
	};

	return (
		<div className="space-y-6">
			{showManualForm ? (
				<ManualBusinessForm
					onSubmit={handleManualBusinessSubmit}
					onBack={handleBackToUrlEntry}
					isSubmitting={isCreatingOrganization}
				/>
			) : !showPreview ? (
				<Form {...form}>
					<form
						className="flex flex-col items-stretch gap-8"
						onSubmit={form.handleSubmit(onSubmit)}
					>
						<FormField
							control={form.control}
							name="googleMapsUrl"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="flex items-center gap-2">
										<MapPinIcon className="size-4" />
										{t(
											"onboarding.business.googleMapsLink",
										)}
									</FormLabel>
									<FormControl>
										<div className="space-y-2">
											<Input
												{...field}
												placeholder={t(
													"onboarding.business.googleMapsLinkPlaceholder",
												)}
												type="url"
												disabled={isExtracting}
											/>
											<div className="flex items-center justify-between">
												<Button
													type="button"
													variant="ghost"
													size="sm"
													onClick={tryDemo}
													disabled={isExtracting}
													className="text-xs h-7"
												>
													Try Demo
												</Button>
												<Badge
													status="info"
													className="text-xs"
												>
													Demo
												</Badge>
											</div>
										</div>
									</FormControl>
									<FormDescription>
										{t(
											"onboarding.business.googleMapsLinkDescription",
										)}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						{form.formState.errors.root &&
							form.formState.errors.root.message ===
								"manual_entry_available" && (
								<div className="space-y-3 p-4 bg-muted/50 rounded-lg border">
									<p className="text-sm text-muted-foreground">
										Having trouble with the URL? You can add
										your business information manually
										instead.
									</p>
									<Button
										type="button"
										variant="outline"
										size="sm"
										onClick={() => setShowManualForm(true)}
										disabled={isExtracting}
										className="w-full"
									>
										Add Business Manually
									</Button>
								</div>
							)}

						<Button
							type="submit"
							loading={
								form.formState.isSubmitting || isExtracting
							}
							disabled={
								form.formState.isSubmitting || isExtracting
							}
						>
							{isExtracting
								? t("onboarding.business.extracting")
								: t("onboarding.continue")}
							<ArrowRightIcon className="ml-2 size-4" />
						</Button>
					</form>
				</Form>
			) : (
				<div className="space-y-6">
					{businessInfo && (
						<ExtendedBusinessPreview
							businessInfo={businessInfo}
							className="mx-auto max-w-md"
						/>
					)}

					{!isCreatingOrganization ? (
						<div className="space-y-4">
							<div className="text-center">
								<p className="text-sm text-muted-foreground mb-4">
									Please review your business information and
									confirm to create your organization.
								</p>
							</div>

							<div className="flex gap-3">
								<Button
									variant="outline"
									onClick={handleBackToUrlEntry}
									className="flex-1"
									disabled={isCreatingOrganization}
								>
									Go Back
								</Button>
								<Button
									onClick={handleConfirmBusiness}
									className="flex-1"
									disabled={isCreatingOrganization}
								>
									Confirm
									<ArrowRightIcon className="ml-2 size-4" />
								</Button>
							</div>
						</div>
					) : (
						<div className="text-center py-8">
							<div className="inline-flex items-center gap-3 px-4 py-3 bg-muted rounded-lg">
								<div className="animate-spin size-4 border-2 border-primary border-t-transparent rounded-full" />
								<span className="text-sm font-medium">
									Creating your website...
								</span>
							</div>
						</div>
					)}
				</div>
			)}
		</div>
	);
}
