"use client";

import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { GlobeIcon, MapPinIcon, PhoneIcon, StarIcon } from "lucide-react";
import type { BusinessInfo } from "../utils/google-maps";

interface BusinessPreviewProps {
	businessInfo: BusinessInfo;
	className?: string;
}

/**
 * Component to preview extracted business information
 * This would be shown after successfully extracting data from Google Maps
 */
export function BusinessPreview({
	businessInfo,
	className,
}: BusinessPreviewProps) {
	return (
		<Card className={className}>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<MapPinIcon className="size-5" />
					Business Information
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				{businessInfo.name && (
					<div>
						<h3 className="font-semibold text-lg">
							{businessInfo.name}
						</h3>
					</div>
				)}

				{businessInfo.address && (
					<div className="flex items-start gap-2">
						<MapPinIcon className="size-4 mt-1 text-muted-foreground" />
						<span className="text-sm">{businessInfo.address}</span>
					</div>
				)}

				{businessInfo.phone && (
					<div className="flex items-center gap-2">
						<PhoneIcon className="size-4 text-muted-foreground" />
						<span className="text-sm">{businessInfo.phone}</span>
					</div>
				)}

				{businessInfo.website && (
					<div className="flex items-center gap-2">
						<GlobeIcon className="size-4 text-muted-foreground" />
						<a
							href={businessInfo.website}
							target="_blank"
							rel="noopener noreferrer"
							className="text-sm text-primary hover:underline"
						>
							{businessInfo.website}
						</a>
					</div>
				)}

				<div className="pt-2 border-t">
					<div className="flex items-center gap-2 text-sm text-muted-foreground">
						<span>Places ID:</span>
						<Badge status="info" className="font-mono text-xs">
							{businessInfo.placesId}
						</Badge>
					</div>
				</div>

				<div className="text-xs text-muted-foreground">
					<a
						href={businessInfo.googleMapsUrl}
						target="_blank"
						rel="noopener noreferrer"
						className="hover:underline"
					>
						View on Google Maps →
					</a>
				</div>
			</CardContent>
		</Card>
	);
}

/**
 * Extended business preview with additional information
 * This shows the full business details including ratings, hours, etc.
 */
interface ExtendedBusinessPreviewProps {
	businessInfo: BusinessInfo;
	className?: string;
}

export function ExtendedBusinessPreview({
	businessInfo,
	className,
}: ExtendedBusinessPreviewProps) {
	return (
		<Card className={className}>
			<CardContent className="p-4">
				{/* Business Name and Rating */}
				<div className="space-y-2 mb-3">
					<h3 className="font-semibold text-base">
						{businessInfo.name || "Business Information"}
					</h3>
					{businessInfo.rating && (
						<div className="flex items-center gap-2 text-sm">
							<div className="flex items-center gap-1">
								<StarIcon className="size-3 fill-yellow-400 text-yellow-400" />
								<span className="font-medium">
									{businessInfo.rating}
								</span>
							</div>
							<span className="text-muted-foreground">
								({businessInfo.userRatingsTotal} reviews)
							</span>
						</div>
					)}
				</div>

				{/* Essential Info Only */}
				<div className="space-y-2 text-sm">
					{businessInfo.address && (
						<div className="flex items-start gap-2">
							<MapPinIcon className="size-3 mt-0.5 text-muted-foreground shrink-0" />
							<span className="text-muted-foreground leading-relaxed">
								{businessInfo.address}
							</span>
						</div>
					)}

					{businessInfo.phone && (
						<div className="flex items-center gap-2">
							<PhoneIcon className="size-3 text-muted-foreground shrink-0" />
							<span className="text-muted-foreground">
								{businessInfo.phone}
							</span>
						</div>
					)}
				</div>
			</CardContent>
		</Card>
	);
}
