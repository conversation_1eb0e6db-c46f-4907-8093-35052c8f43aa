# Onboarding Module - Google Maps Business Integration

This module has been modified to collect Google Maps business links during the onboarding process instead of user avatar and name.

## Changes Made

### 1. OnboardingStep1 Component
- **File**: `components/OnboardingStep1.tsx`
- **Changes**:
  - Replaced name and avatar fields with Google Maps business link field
  - Added form validation for Google Maps URLs
  - Added Places ID extraction functionality
  - Creates organization with business name and stores business data in metadata
  - User keeps their original name, business becomes an organization

### 2. Translation Keys
- **Files**: `packages/i18n/translations/{en,fr,de}.json`
- **New Keys**:
  - `onboarding.business.googleMapsLink`: Field label
  - `onboarding.business.googleMapsLinkPlaceholder`: Input placeholder
  - `onboarding.business.googleMapsLinkDescription`: Field description
  - `onboarding.business.extracting`: Loading state text
  - `onboarding.title`: Updated to "Set up your business"

### 3. Utility Functions
- **File**: `utils/google-maps.ts`
- **Functions**:
  - `extractPlacesIdFromUrl()`: Extract Places ID from various Google Maps URL formats
  - `isGoogleMapsUrl()`: Validate if URL is a Google Maps URL
  - `extractBusinessInfo()`: Extract business information (placeholder for API integration)
  - `getPlacesApiUrl()`: Helper for Google Places API URL construction

### 4. API Route
- **File**: `app/api/business/extract-from-maps/route.ts`
- **Purpose**: Backend endpoint for extracting business information from Google Maps URLs
- **Status**: Basic implementation ready, Google Places API integration commented out

## Setup Instructions

### 1. Environment Variables
Add the following to your `.env.local` file:

```bash
# Google Maps Places API
GOOGLE_MAPS_API_KEY="your_google_maps_api_key_here"
```

### 2. Google Maps API Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the "Places API"
4. Create an API key
5. Restrict the API key to your domain (recommended for production)

### 3. Organization Structure
The system now creates organizations with business information stored in metadata:

```typescript
// Organization metadata structure
{
  businessInfo: {
    placesId: string;
    googleMapsUrl: string;
    name: string;
    address: string;
    phone: string;
    website: string;
    rating: number;
    userRatingsTotal: number;
    openingHours: string[];
    photos: Array<{reference: string, width: number, height: number}>;
    extractedAt: string;
    source: 'google_maps' | 'google_maps_demo';
  }
}
```

## Supported Google Maps URL Formats

The system currently supports these URL formats:

1. **CID Format**: `https://maps.google.com/maps?cid=123456789`
2. **Place ID Format**: `https://www.google.com/maps/place/Business+Name/@lat,lng,zoom/data=!3m1!4b1!4m6!3m5!1s0x123:0x456`
3. **Shortened URLs**: `https://goo.gl/maps/...` (requires URL resolution)

## Future Enhancements

### 1. Full Google Places API Integration
Uncomment the API integration code in `app/api/business/extract-from-maps/route.ts` and implement:
- Business name extraction
- Address parsing
- Phone number retrieval
- Website URL extraction
- Business hours
- Photos and ratings

### 2. Database Integration
- Add proper database fields for business information
- Store extracted data during onboarding
- Use business data for website generation

### 3. Error Handling
- Add retry logic for API failures
- Implement fallback for unsupported URL formats
- Add user-friendly error messages

### 4. Validation Improvements
- Real-time URL validation
- Preview business information before submission
- Support for more Google Maps URL formats

## Testing

To test the implementation:

1. Start the development server
2. Navigate to the onboarding page
3. Enter a Google Maps business URL
4. Verify that the Places ID is extracted correctly
5. Check that the form validation works for invalid URLs

## Demo Mode - Test URLs

The system now includes demo mode with fake business data. Try these URLs to see different demo businesses:

### Coffee Shop Demo
```
https://maps.google.com/maps?cid=1234567890123456789
```
**Shows**: The Artisan Coffee Co. in SoHo, NYC with 4.7 stars, detailed hours, and premium coffee branding.

### Restaurant Demo
```
https://www.google.com/maps/place/Restaurant/@40.7128,-74.0060,15z/data=!3m1!4b1!4m6!3m5!1s0x89c25a316e5b7c5d:0x9876543210fedcba
```
**Shows**: Nonna's Italian Kitchen in San Francisco with 4.9 stars and authentic Italian dining experience.

### Tech Service Demo
```
https://maps.google.com/maps?cid=5555666677778888
```
**Shows**: Digital Solutions Hub in Palo Alto with 4.4 stars and comprehensive tech services.

### Florist Demo
```
https://maps.google.com/maps?cid=7777888899990000
```
**Shows**: Bloom & Blossom Florist in Beverly Hills with 4.8 stars and luxury floral arrangements.

### Fitness Center Demo
```
https://www.google.com/maps/place/Fitness/@41.8781,-87.6298,15z/data=!3m1!4b1!4m6!3m5!1s0x880e2ca55810a493:0x1111222233334444
```
**Shows**: Metro Fitness Center in Chicago with 4.3 stars and extensive gym facilities.

### Invalid URLs (for testing error handling)
```
- https://example.com
- https://maps.google.com (without business information)
- not-a-valid-url
```

## Demo Flow

1. **Enter URL**: Paste any of the demo URLs above
2. **Validation**: Form validates the URL format and extracts Places ID
3. **API Call**: System calls `/api/business/extract-from-maps` with demo data
4. **Loading State**: Shows "Extracting business data..." for 1.5 seconds
5. **Success Preview**: Displays extracted business information with:
   - Business name and address
   - Phone number and website
   - Star rating and review count
   - Opening hours
   - Google Maps link
6. **Manual Confirmation**: User reviews business info and clicks "Create Organization"
7. **Organization Creation**: Creates new organization with business name and metadata
8. **Completion**: Completes onboarding and redirects to business organization dashboard

## Manual Confirmation Flow

The onboarding now includes a confirmation step for better user control:

### Preview Stage
- Shows extracted business information in clean card format
- User can review all details before proceeding
- "Go Back" button allows editing the Google Maps URL
- "Create Organization" button proceeds with organization creation

### Confirmation Stage
- Shows loading state: "Creating your organization..."
- Prevents accidental double-clicks with disabled buttons
- Creates organization with business data in background
- Redirects to business dashboard upon completion

**Perfect for business users** who want to review their business information before committing to organization creation! 🎉
