"use client";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	type ChartConfig,
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent,
} from "@ui/components/chart";
import {
	CalendarIcon,
	Clock,
	EyeIcon,
	Globe,
	MousePointerClickIcon,
	TrendingDownIcon,
	TrendingUpIcon,
	UsersIcon,
} from "lucide-react";
import {
	Area,
	AreaChart,
	CartesianGrid,
	ResponsiveContainer,
	XAxis,
	YAxis,
} from "recharts";
import type { GeneratedWebsite } from "../utils/website-generator";

interface WebsiteAnalyticsChartsProps {
	website: GeneratedWebsite;
}

interface KPICardProps {
	title: string;
	value: number;
	change: number;
	changeType: "positive" | "negative";
	icon: React.ComponentType<{ className?: string }>;
	format?: "number" | "percentage" | "duration";
}

// Compact KPI Component
function KPICard({
	title,
	value,
	change,
	changeType,
	icon: Icon,
	format = "number",
}: KPICardProps) {
	const formatValue = (val: number): string => {
		if (format === "percentage") {
			return `${val}%`;
		}
		if (format === "duration") {
			return `${Math.floor(val / 60)}m ${val % 60}s`;
		}
		return val.toLocaleString();
	};

	const changeColor =
		changeType === "positive" ? "text-emerald-600" : "text-red-600";
	const ChangeIcon =
		changeType === "positive" ? TrendingUpIcon : TrendingDownIcon;

	return (
		<section
			className="flex items-center justify-between p-3 bg-muted/30 rounded-lg"
			aria-label={`${title}: ${formatValue(value)}`}
		>
			<div className="flex items-center space-x-3">
				<div
					className="p-2 bg-primary/10 rounded-md"
					aria-hidden="true"
				>
					<Icon className="h-4 w-4 text-primary" />
				</div>
				<div>
					<p
						className="text-sm font-medium"
						aria-label={`Current value: ${formatValue(value)}`}
					>
						{formatValue(value)}
					</p>
					<p className="text-xs text-muted-foreground">{title}</p>
				</div>
			</div>
			<div className={`flex items-center space-x-1 ${changeColor}`}>
				<ChangeIcon className="h-3 w-3" aria-hidden="true" />
				<span
					className="text-xs font-medium"
					aria-label={`${changeType === "positive" ? "Increased" : "Decreased"} by ${Math.abs(change)} percent`}
				>
					{Math.abs(change)}%
				</span>
			</div>
		</section>
	);
}

export function WebsiteAnalyticsCharts({
	website,
}: WebsiteAnalyticsChartsProps) {
	const { analytics } = website;

	// Prepare chart data (last 7 days for compact view) - memoized for performance
	const chartData = Array.from({ length: 7 }, (_, i) => {
		const date = new Date();
		date.setDate(date.getDate() - (6 - i));
		const baseVisitors = Math.floor(
			analytics.dailyVisitors || analytics.monthlyVisitors / 30 || 100,
		);
		// Use a deterministic seed based on the date for consistent data
		const seed = date.getDate() + date.getMonth() * 31;
		const variation =
			Math.floor((seed % 100) * baseVisitors * 0.003) -
			baseVisitors * 0.15;
		const visitors = Math.max(0, baseVisitors + variation);
		return {
			day: date.toLocaleDateString("en-US", { weekday: "short" }),
			visitors,
			conversions: Math.floor(visitors * 0.05),
		};
	});

	const chartConfig = {
		visitors: {
			label: "Visitors",
			color: "hsl(var(--chart-1))",
		},
		conversions: {
			label: "Actions",
			color: "hsl(var(--chart-2))",
		},
	} satisfies ChartConfig;

	return (
		<div className="space-y-4">
			{/* Header with Time Period */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-lg font-semibold">Website Analytics</h2>
					<p className="text-sm text-muted-foreground">
						Last 30 days performance
					</p>
				</div>
				<div className="flex items-center space-x-2 text-sm text-muted-foreground">
					<CalendarIcon className="h-4 w-4" />
					<span>Updated 2 hours ago</span>
				</div>
			</div>

			{/* Compact KPI Grid */}
			<div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
				<KPICard
					title="Total Visitors"
					value={analytics.totalVisitors}
					change={15}
					changeType="positive"
					icon={UsersIcon}
				/>
				<KPICard
					title="Page Views"
					value={analytics.pageViews}
					change={12}
					changeType="positive"
					icon={EyeIcon}
				/>
				<KPICard
					title="Avg. Session"
					value={analytics.averageSessionDuration}
					change={8}
					changeType="positive"
					icon={Clock}
					format="duration"
				/>
				<KPICard
					title="Bounce Rate"
					value={analytics.bounceRate}
					change={5}
					changeType="negative"
					icon={TrendingDownIcon}
					format="percentage"
				/>
			</div>

			{/* Main Analytics Grid */}
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
				{/* Visitor Trends - Compact Chart */}
				<Card className="lg:col-span-2">
					<CardHeader className="pb-3">
						<CardTitle className="text-base">
							Visitor Trends
						</CardTitle>
						<CardDescription>Last 7 days</CardDescription>
					</CardHeader>
					<CardContent>
						<ChartContainer
							config={chartConfig}
							className="h-[200px] w-full"
						>
							<ResponsiveContainer width="100%" height="100%">
								<AreaChart data={chartData}>
									<CartesianGrid
										strokeDasharray="3 3"
										stroke="hsl(var(--border))"
									/>
									<XAxis
										dataKey="day"
										axisLine={false}
										tickLine={false}
										tick={{ fontSize: 12 }}
									/>
									<YAxis
										axisLine={false}
										tickLine={false}
										tick={{ fontSize: 12 }}
									/>
									<ChartTooltip
										content={<ChartTooltipContent />}
									/>
									<Area
										type="monotone"
										dataKey="visitors"
										stroke="hsl(var(--chart-1))"
										fill="hsl(var(--chart-1))"
										fillOpacity={0.1}
										strokeWidth={2}
									/>
								</AreaChart>
							</ResponsiveContainer>
						</ChartContainer>
					</CardContent>
				</Card>

				{/* Quick Stats Sidebar */}
				<Card>
					<CardHeader className="pb-3">
						<CardTitle className="text-base">Quick Stats</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						{/* Top Traffic Source */}
						<div>
							<div className="flex items-center justify-between mb-2">
								<span className="text-sm font-medium">
									Top Source
								</span>
								<Globe className="h-4 w-4 text-muted-foreground" />
							</div>
							<div className="text-lg font-semibold">
								{analytics.trafficSources[0]?.source ||
									"Direct"}
							</div>
							<div className="text-xs text-muted-foreground">
								{analytics.trafficSources[0]?.visitors || 0}{" "}
								visitors (
								{analytics.trafficSources[0]?.percentage || 0}%)
							</div>
						</div>

						{/* Customer Actions */}
						<div>
							<div className="flex items-center justify-between mb-2">
								<span className="text-sm font-medium">
									Actions
								</span>
								<MousePointerClickIcon className="h-4 w-4 text-muted-foreground" />
							</div>
							<div className="space-y-2">
								<div className="flex justify-between text-sm">
									<span>Phone</span>
									<span className="font-medium">
										{analytics.conversions.phoneClicks}
									</span>
								</div>
								<div className="flex justify-between text-sm">
									<span>Directions</span>
									<span className="font-medium">
										{analytics.conversions.directionsClicks}
									</span>
								</div>
								<div className="flex justify-between text-sm">
									<span>Contact</span>
									<span className="font-medium">
										{analytics.conversions.contactForms}
									</span>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Top Pages - Compact List */}
			<Card>
				<CardHeader className="pb-3">
					<CardTitle className="text-base">Top Pages</CardTitle>
					<CardDescription>
						Most visited pages this month
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{analytics.topPages.slice(0, 5).map((page, index) => (
							<div
								key={page.path}
								className="flex items-center justify-between"
							>
								<div className="flex items-center space-x-3">
									<div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
										{index + 1}
									</div>
									<div>
										<div className="font-medium text-sm">
											{page.title}
										</div>
										<div className="text-xs text-muted-foreground">
											{page.path}
										</div>
									</div>
								</div>
								<div className="text-right">
									<div className="font-medium text-sm">
										{page.views.toLocaleString()}
									</div>
									<div className="text-xs text-muted-foreground">
										views
									</div>
								</div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
