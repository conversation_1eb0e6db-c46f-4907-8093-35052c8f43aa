"use client";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import { ExternalLinkIcon, GlobeIcon } from "lucide-react";
import {} from "react";
import type { GeneratedWebsite } from "../utils/website-generator";

// Individual SettingsItem components
function WebsiteOverviewItem({
	website,
	templateInfo,
	onSettingsClick,
}: {
	website: GeneratedWebsite;
	templateInfo: any;
	onSettingsClick: () => void;
}) {
	const { status, url } = website;

	return (
		<SettingsItem
			title="Your Business Website"
			description={templateInfo.description}
		>
			<div className="flex items-center justify-between">
				<div className="flex items-center space-x-3">
					<div className="w-8 h-8 bg-black dark:bg-white rounded-md flex items-center justify-center">
						<GlobeIcon className="h-4 w-4 text-white dark:text-black" />
					</div>
					<div className="flex items-center space-x-2">
						<div
							className={`w-2 h-2 rounded-full ${
								status === "active"
									? "bg-green-500"
									: status === "generating"
										? "bg-yellow-500"
										: "bg-red-500"
							}`}
						/>
						<span className="text-sm font-medium">
							{status === "active"
								? "Live & Working"
								: status === "generating"
									? "Setting Up..."
									: "Needs Attention"}
						</span>
					</div>
				</div>
				<Button size="sm" asChild>
					<a href={url} target="_blank" rel="noopener noreferrer">
						<ExternalLinkIcon className="h-3 w-3 mr-1" />
						View My Website
					</a>
				</Button>
			</div>
		</SettingsItem>
	);
}

function WebsiteAddressItem({
	website,
	templateInfo,
}: {
	website: GeneratedWebsite;
	templateInfo: any;
}) {
	const { url } = website;

	return (
		<SettingsItem
			title="Your Website Address"
			description="Your website URL and domain information"
		>
			<div className="bg-muted/30 rounded-md p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center space-x-3">
						<div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
							<div className="w-2 h-2 bg-green-600 rounded-full" />
						</div>
						<div>
							<p className="font-mono text-sm">
								{url
									.replace("https://", "")
									.replace("http://", "")}
							</p>
							<p className="text-xs text-muted-foreground">
								Last updated on{" "}
								{new Date(website.updatedAt).toLocaleDateString(
									"en-US",
									{
										month: "short",
										day: "numeric",
										hour: "2-digit",
										minute: "2-digit",
									},
								)}{" "}
								• Using {templateInfo.name} design
							</p>
						</div>
					</div>
					<div className="flex items-center space-x-2">
						<Button variant="ghost" size="sm" asChild>
							<a
								href={url}
								target="_blank"
								rel="noopener noreferrer"
							>
								<ExternalLinkIcon className="h-3 w-3" />
							</a>
						</Button>
					</div>
				</div>
			</div>
		</SettingsItem>
	);
}

function WebsiteStatsItem({
	website,
	templateInfo,
}: {
	website: GeneratedWebsite;
	templateInfo: any;
}) {
	const { status } = website;

	return (
		<SettingsItem
			title="Website Summary"
			description="Key information about your website"
		>
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground uppercase tracking-wide">
						Current Status
					</p>
					<p className="font-semibold">
						{status === "active"
							? "Live & Working"
							: status === "generating"
								? "Setting Up..."
								: "Needs Attention"}
					</p>
				</div>
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground uppercase tracking-wide">
						Website Design
					</p>
					<p className="font-semibold">{templateInfo.name}</p>
				</div>
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground uppercase tracking-wide">
						Last Updated
					</p>
					<p className="font-semibold">
						{Math.floor(
							(Date.now() -
								new Date(website.updatedAt).getTime()) /
								(1000 * 60 * 60 * 24),
						) === 0
							? "Today"
							: Math.floor(
										(Date.now() -
											new Date(
												website.updatedAt,
											).getTime()) /
											(1000 * 60 * 60 * 24),
									) === 1
								? "Yesterday"
								: `${Math.floor(
										(Date.now() -
											new Date(
												website.updatedAt,
											).getTime()) /
											(1000 * 60 * 60 * 24),
									)} days ago`}
					</p>
				</div>
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground uppercase tracking-wide">
						Web Address Type
					</p>
					<p className="font-semibold text-sm">Your Own Domain</p>
				</div>
			</div>
		</SettingsItem>
	);
}

export function WebsiteDashboard() {
	const { activeOrganization } = useActiveOrganization();
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [settingsOpen, setSettingsOpen] = useState(false);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) return;

		try {
			setLoading(true);
			const websiteData = await WebsiteService.getWebsiteByOrganization(
				activeOrganization.id,
			);
			setWebsite(websiteData);
			setError(null);
		} catch (err) {
			setError("Failed to load website data");
			console.error("Error loading website:", err);
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3">
					<LoaderIcon className="size-5 animate-spin" />
					<span className="text-muted-foreground">
						Getting your website ready...
					</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3 text-destructive">
					<AlertCircleIcon className="size-5" />
					<span>
						We couldn't load your website right now. Please try
						refreshing the page.
					</span>
				</div>
			</div>
		);
	}

	if (!website) {
		return (
			<div className="text-center py-12">
				<div className="max-w-md mx-auto">
					<GlobeIcon className="size-12 mx-auto text-muted-foreground mb-4" />
					<h3 className="text-lg font-semibold mb-2">
						Your Website Isn't Ready Yet
					</h3>
					<p className="text-muted-foreground mb-4">
						Don't worry! We're still setting up your website. This
						usually takes just a few minutes after you sign up.
					</p>
					<Button onClick={loadWebsite}>Check Again</Button>
				</div>
			</div>
		);
	}

	const { config, status, url } = website;
	const templateInfo = WebsiteService.getTemplateInfo(config.template);

	return (
		<SettingsList>
			<WebsiteOverviewItem
				website={website}
				templateInfo={templateInfo}
				onSettingsClick={() => setSettingsOpen(true)}
			/>

			<WebsiteAddressItem website={website} templateInfo={templateInfo} />

			<WebsiteStatsItem website={website} templateInfo={templateInfo} />

			<WebsiteAnalyticsCharts website={website} />

			{/* Settings Modal */}
			{website && (
				<WebsiteSettingsModal
					website={website}
					open={settingsOpen}
					onOpenChange={setSettingsOpen}
					onWebsiteUpdate={setWebsite}
				/>
			)}
		</SettingsList>
	);
}
