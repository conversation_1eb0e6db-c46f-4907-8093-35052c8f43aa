{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"allowJs": true, "composite": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "incremental": true, "inlineSources": false, "isolatedModules": true, "jsx": "preserve", "module": "Preserve", "target": "ES2021", "moduleResolution": "bundler", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true}, "display": "<PERSON><PERSON><PERSON>", "exclude": ["node_modules", "dist", ".next", "build"]}